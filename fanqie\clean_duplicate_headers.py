#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理番茄小说下载文件中重复的书籍头部信息
"""

import os
import re
import glob

def clean_duplicate_headers(file_path: str):
    """清理文件中重复的书籍头部信息"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    print(f"正在处理: {file_path}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文件大小
        original_size = len(content)
        
        # 使用正则表达式匹配书籍头部信息块
        # 匹配模式：书名 -> 作者 -> 最新章节 -> 简介 -> 总章节数 -> 分隔线
        header_pattern = r'书名：[^\n]*\n作者：[^\n]*\n最新章节：[^\n]*\n简介：[^\n]*\n总章节数：[^\n]*\n=+\n*'
        
        # 找到所有匹配的头部
        headers = list(re.finditer(header_pattern, content))
        
        if len(headers) <= 1:
            print(f"  ✓ 无需清理（头部数量: {len(headers)}）")
            return True
        
        print(f"  发现 {len(headers)} 个重复的书籍头部")
        
        # 保留第一个头部，移除其他的
        cleaned_content = content
        
        # 从后往前删除，避免位置偏移问题
        for i, header_match in enumerate(reversed(headers[1:])):
            start, end = header_match.span()
            print(f"  移除第 {len(headers) - i} 个重复头部 (位置: {start}-{end})")
            cleaned_content = cleaned_content[:start] + cleaned_content[end:]
        
        # 检查清理效果
        new_size = len(cleaned_content)
        saved_bytes = original_size - new_size
        
        # 备份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  原文件已备份到: {backup_path}")
        
        # 写入清理后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"  ✓ 清理完成！节省了 {saved_bytes} 字节 ({saved_bytes/1024:.1f} KB)")
        return True
        
    except Exception as e:
        print(f"  ✗ 清理失败: {e}")
        return False

def clean_all_books(download_dir: str = "downloads"):
    """清理指定目录下所有的小说文件"""
    if not os.path.exists(download_dir):
        print(f"下载目录不存在: {download_dir}")
        return
    
    print(f"开始清理目录: {download_dir}")
    print("=" * 50)
    
    # 查找所有的完整小说文件
    pattern = os.path.join(download_dir, "**", "*_complete.txt")
    book_files = glob.glob(pattern, recursive=True)
    
    if not book_files:
        print("未找到任何小说文件")
        return
    
    print(f"找到 {len(book_files)} 个小说文件")
    print()
    
    success_count = 0
    error_count = 0
    
    for book_file in book_files:
        try:
            if clean_duplicate_headers(book_file):
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            print(f"处理文件 {book_file} 时出错: {e}")
            error_count += 1
        print()
    
    print("=" * 50)
    print(f"清理完成！成功: {success_count}, 失败: {error_count}")

if __name__ == "__main__":
    # 可以指定特定文件或清理整个目录
    import sys
    
    if len(sys.argv) > 1:
        target = sys.argv[1]
        if os.path.isfile(target):
            # 清理单个文件
            clean_duplicate_headers(target)
        elif os.path.isdir(target):
            # 清理整个目录
            clean_all_books(target)
        else:
            print(f"路径不存在: {target}")
    else:
        # 默认清理 downloads 目录
        clean_all_books("downloads")
