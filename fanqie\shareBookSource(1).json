[{"bookSourceComment": "搜索格式\n搜索用户<关闭精确搜索>\n@用户名\n搜索合集<关闭精确搜索>\n#合集\n搜索文章，直接搜索\n发现规则\n                                    🏷标签\n标签名::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=标签名&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n📃用户（搜索可以得到)", "bookSourceGroup": "Fun", "bookSourceName": "乐乎", "bookSourceType": 0, "bookSourceUrl": "https://newsmiss.lofter.com#♤guaner", "bookUrlPattern": "", "customOrder": 611, "enabled": true, "enabledCookieJar": false, "enabledExplore": true, "exploreUrl": "✱　　　　　　✱    测    试    ✱　　　　　　✱::\n\n歲月之聲::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\":\"POST\",\"body\":\"supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain=awake999.lofter.com&offset={{(page-1)*18}}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}\n\n漫画::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=漫画&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n\n纳兰妙殊::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\":\"POST\",\"body\":\"supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain=wisely1229.lofter.com&offset={{(page-1)*20}}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}\n\n✱　　　　　　✱    标    签    ✱　　　　　　✱::\n漫画::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=漫画&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n\n百合::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=百合&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n微微怡笑::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=微微怡笑&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n双镜::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=双镜&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n玉梦::https://api.lofter.com/oldapi/tagPosts.api?product=lofter-android-6.13.0&method=newTagSearch&offset={{(page-1) *22}}&limit=22&firstpermalink=null&tag=玉梦&type=new,{\"method\": \"POST\",\"body\":\"null\"}\n✱　　　　　　✱    用    户    ✱　　　　　　✱::\n纳兰妙殊::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\":\"POST\",\"body\":\"supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain=wisely1229.lofter.com&offset={{(page-1)*20}}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}\n\n砂上雪::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.13.0,{\"method\":\"POST\",\"body\":\"targetblogid=514131314&supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain=taste-s.lofter.com&offset={{(page-1)*18}}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}", "header": "{\n\"Content-Type\": \"application/x-www-form-urlencoded;charset=utf-8\"}", "lastUpdateTime": 1753685660346, "loginUrl": "https://newsmiss.lofter.com", "respondTime": 73038, "ruleBookInfo": {"author": "$.response.blogInfo.blogNickName||$.response.posts[0].post.blogInfo.blogNickName", "canReName": "1", "coverUrl": "$.response.collection.coverUrl@put:{page:$.response.blogInfo.blogStat.publicPostCount}", "intro": "@js:\na='{{$.response.collection.description}}';\nb='{{$.response.blogInfo.blogNickName}}';\nc=java.getString('$.response.posts[0].post.digest||$.response.collection.description');\nif(!c){\nresult=a+'\\n'+String(book.intro).replace(/@.*?::/,b?b+'::':book.author+'::');\n}else{result=c}\n\ncname=\"{{$.response.posts[0].post.postCollection.name}}\";\ncdes=java.getString('$.response.posts[0].post.postCollection.description');\nccount=\"{{$.response.posts[0].post.postCollection.postCount}}\";\nif(cname){\nresult=result+\"\\n&lrm;\\n所属合集：\"+cname+\"\\n合集介绍：\"+cdes+\"\\n\"+\"合集章节总数：\"+ccount\n+\"\\n搜索合集：#\"+cname+\"<关闭精确搜索>\"}\nresult", "kind": "$.postCollection", "lastChapter": "", "name": "$.response.collection.name", "tocUrl": "$.response.blogsetting.blogId\n@js:\nif(result){\nid='{{$.response.blogsetting.blogId}}';\nlink='{{$.response.blogLink}}';\nurl='http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,';\npost={\"method\": \"POST\",\n\"body\": \"targetblogid=\"+id+\"&supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain=\"+link+\"&offset=0&method=getPostLists&postdigestnew=1&returnData=1&limit=50&checkpwd=1&needgetpoststat=1\"}\nresult=url+JSON.stringify(post);\njava.put('url',result)}else if(baseUrl.match(/detail/)){\nresult='{{$.response.posts[0].post.blogPageUrl}}'\n}else if(baseUrl.match(/postCollection/)){\nresult=java.get('url')\n}"}, "ruleContent": {"content": "@css:.g-mnc,div[class~=cont],.main\n.box,.ct@html\n<js>\nresult.replace(/thumbnail=\\d+x\\d+/g,'thumbnail=2340x0')\n</js>\n##(.*?class=\"nctitle\">|<!-- Pager -->)[\\s\\S]+|(?<=\\<p\\>)(\\s|&nbsp;)*", "imageStyle": "full", "nextContentUrl": "", "replaceRegex": "##src=\"(.*?)\"##src=\"$1,{'webView':true}\"", "sourceRegex": ""}, "ruleExplore": {"author": "$.post.blogInfo.blogNickName", "bookList": "<js>result.replace(/null/g,'')</js>\n$.response.items[*]||$.response.posts[*]", "bookUrl": "@js:url='{{$.post.blogPageUrl}}';\nid='{{$.post.id}}';\nif(url.match(/_blogid_\\d+/)){\nmain=url.match(/(_blogid_.*?)\\//)[1];\nbody=\"blogdomain=\"+main+\"&postid=\"+id;\nresult='https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-6.11.3,{\"method\":\"POST\",\"body\":\"'+String(body)+'\"}';\n}else{result=url}", "coverUrl": "$.post\n@js:if(result.match(/firstImageUrl=\\[\"\",\"\"\\]/)){\nresult=result.match(/bigAvaImg=(.*?),/)?result.match(/bigAvaImg=(.*?),/)[1]:'';}else{\nresult=result.match(/firstImageUrl=\\[\"([^\"]+)\"/)?result.match(/firstImageUrl=\\[\"([^\"]+)\"/)[1]:\"\"\n}", "intro": "{{$.post.digest}}\n★★★★★★★★★★★★★★★★★★★★\n复制下面的文字，可将用户添加发现或者订阅\n★★★★★★★★★★★★★★★★★★★★\n{{$.post.blogInfo.blogNickName}}::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\":\"POST\",\"body\":\"targetblogid={{$.post.blogInfo.blogId}}&supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain={{$.post.blogInfo.blogName}}.lofter.com&offset={{'\\{\\{(page-1)*18\\}\\}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}'}}", "kind": "$.post.tag", "lastChapter": "$.post.publishTime\n@js:result=java.timeFormat(parseInt(result));\njava.put('time',result)", "name": "$.post.title||$.post.digest||$.post.blogInfo.blogId||$.post.blogPageUrl##</*\\w.*?>"}, "ruleReview": {}, "ruleSearch": {"author": "$.blogInfo.blogNickName||$.blogName", "bookList": "$.data.posts[*]||$.data.blogs[*]||$.data.collections[*]", "bookUrl": "$.postPageUrl\n@js:\nif(!result){\nif(!'{{$.name}}'){\nid='{{$.blogId}}';\nresult='http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\": \"POST\",\"body\":\"targetblogid='+id+'&method=getBlogInfoDetail&returnData=1&checkpwd=1&needgetpoststat=1\"}'}else{\nblogid='{{$.blogId}}';\nid='{{$.id}}';\nname='{{$.blogName}}';\njava.put('name',name);\nresult='https://api.lofter.com/v1.1/postCollection.api?product=lofter-android-6.11.1,{\"method\": \"POST\",\"body\":\"blogdomain='+name+'.lofter.com&method=getCollectionSimple&offset=0&limit=2000&blogid='+blogid+'&collectionid='+id+'&order=1\"}';java.put('url',result)\n}}else{result=result};", "coverUrl": "$.firstImageUrl[0]||$.blogInfo.bigAvaImg||$.bigAvaImg||$.coverUrl", "intro": "{{$.digest}}\n★★★★★★★★★★★★★★★★★★★★\n复制下面的文字，可将用户添加发现或者订阅\n★★★★★★★★★★★★★★★★★★★★\n@{{$.blogInfo.blogNickName||$.blogNickName||$.blogName}}::http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-6.9.2,{\"method\":\"POST\",\"body\":\"supportposttypes=1%2C2%2C3%2C4%2C5%2C6&blogdomain={{$.blogName||$.blogInfo.blogName}}.lofter.com&offset={{'\\{\\{(page-1)*18\\}\\}&method=getPostLists&postdigestnew=1&returnData=1&limit=18&checkpwd=1&needgetpoststat=1\"}'}}", "kind": "$.tagList||$.tags", "lastChapter": "$.publishTime\n<js>\nif(result){\nresult?result=java.timeFormat(parseInt(result)):'';\njava.put('time',result)}else{\nresult=java.getString('$.posts[0].title||$.posts[0].digest')\n}</js>\n##</*\\w+.*?>", "name": "$.title||$.digest||$.blogInfo.blogNiceName||$.blogNickName||$.name##</*\\w.*?>", "wordCount": ""}, "ruleToc": {"chapterList": "$..posts[*]||$.response.items[*]\n@js:if(String(result).match(/blogPageUrl/)==null){java.getElements('@@tag.html')}else{result=result}", "chapterName": "$.post.title||$.post.digest||$.post.blogInfo.blogNickName\n<js>if(result){\nresult=result\n}else{result=book.name}\nString(result).replace(/<\\/*\\w.*?>/g,'')\n</js>\n##(^.{1,25})##$1###", "chapterUrl": "$.post.blogPageUrl\n<js>\nif(baseUrl.indexOf('postCollection')>-1){'https://'+java.get('name')+'.lofter.com/post/{{$.post..permalink}}'}else{result=result}\n</js>\n<js>\nresult+',{\"webView\":true}'\n</js>", "nextTocUrl": "<js>\npages=java.get('page');\npage=parseInt(pages/50);\nurl=String(java.get('url'));\nlist=[];\nif(page>6){\npage=6\n}else{page=page}\nfor(i=1;i<=page;i++){\nlist.push(url.replace(/offset=\\d+/,'offset='+(parseInt(i)*50)))\n}\nlist\n</js>", "updateTime": "$.post.publishTime\n@js:result?java.timeFormat(parseInt(result)):java.get('time')"}, "searchUrl": "@js:\nif(key.match(/@/)){\nresult='https://api.lofter.com/newsearch/blog.json?key='+encodeURI(key.match(/^@(.*)/)[1])+'&offset={\\{(page-1) *20}\\}'}else if(key.indexOf('#')==0){\nresult='https://api.lofter.com/newsearch/collection.json?key='+encodeURI(key.match(/^#(.*)/)[1])+'&offset={\\{(page-1) *20}\\}'\n}else{\nresult='https://api.lofter.com/newsearch/post.json?key='+key+'&offset={\\{(page-1) *20}\\}&limit=20'\n}", "weight": 0}]