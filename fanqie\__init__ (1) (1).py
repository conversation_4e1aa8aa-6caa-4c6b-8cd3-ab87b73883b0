import html
import os
import tempfile
from urllib.parse import urlparse, parse_qs
import httpx
from nonebot import on_message, on_regex
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from contextlib import asynccontextmanager
import base64
from bs4 import BeautifulSoup
import asyncio
import zipfile
import re
import uuid
import time
import sys
from urllib.parse import quote

# 导入插件管理器
from ..plugin_manager import PluginPriority, log_plugin_info, conditional_block, is_platform_command
from nonebot.rule import Rule

# 添加WebDAV配置
WEBDAV_URL = os.getenv("WEBDAV_URL", "https://al.zyii.xyz:666/dav")  # WebDAV服务器地址
WEBDAV_USERNAME = os.getenv("WEBDAV_USERNAME", "yuedu")  # WebDAV用户名
WEBDAV_PASSWORD = os.getenv("WEBDAV_PASSWORD", "123456")  # WebDAV密码
WEBDAV_FOLDER = os.getenv("WEBDAV_FOLDER", "琪露诺上传的《小说》")  # WebDAV上传目录
WEBDAV_FULL_PATH = os.getenv("WEBDAV_FULL_PATH", "琪露诺上传的《小说》")  # WebDAV完整路径

# 修改 API_BASE_URL 为列表
API_BASE_URLS = [
    "http://************:9090",  # 主要API地址
    "http://************:9011",
    "http://************:9022",
    "http://************:9090",
    "http://************:9011"
]

# 添加一个函数来获取API URL
def get_api_url(index: int = 0) -> str:
    return API_BASE_URLS[index % len(API_BASE_URLS)]

# 存储下载状态的字典
download_status = {}

# 创建番茄专属规则函数
def fanqie_url_rule() -> Rule:
    async def _rule(event: Event) -> bool:
        if not isinstance(event, GroupMessageEvent):
            return False
        raw_message = str(event.get_message()).strip()
        # 增加日志记录，帮助调试
        logger.info(f"番茄链接规则检查消息: {raw_message}")
        
        # 匹配各种番茄小说链接格式
        patterns = [
            # 标准格式
            r"https?://(?:[\w-]+\.)*(?:fanqie|changdunovel|fanqienovel|fqnovel|novel\.(?:snssdk|ixigua)\.com).*?book_id=[0-9]+",
            # 分享格式v2
            r"https?://(?:[\w-]+\.)*changdunovel\.com/wap/share-v2\.html\?.*?book_id=[0-9]+",
            # 其他可能的格式
            r"https?://(?:[\w-]+\.)*(?:fanqie|changdunovel|fanqienovel|fqnovel)\.com/.*\?.*book_id=[0-9]+"
        ]
        
        # 检查所有模式
        for pattern in patterns:
            if re.search(pattern, raw_message):
                logger.info(f"成功匹配番茄小说链接，使用模式: {pattern}")
                return True
                
        return False
    return Rule(_rule)

# 创建一个特别精确的正则匹配，匹配番茄小说链接
# 使用优先级系统，设置为HIGH优先级
download_novel = on_regex(
    # 这个正则表达式只用于触发匹配器，实际匹配由自定义规则完成
    r"https?://(?:[\w-]+\.)*(?:fanqie|changdunovel|fanqienovel|fqnovel|novel\.(?:snssdk|ixigua)\.com)", 
    priority=PluginPriority.HIGH,
    block=True,
    rule=fanqie_url_rule()
)

# 记录插件信息
log_plugin_info("番茄小说链接下载", "番茄小说链接", PluginPriority.HIGH, True)

# 保留下载进度查询功能，提高优先级
progress_query = on_message(priority=PluginPriority.CRITICAL)

# 记录插件信息
log_plugin_info("下载进度查询", "下载进度", PluginPriority.CRITICAL, False)

# 添加全局消息处理器来捕获任何可能的番茄小说链接
fanqie_global_matcher = on_message(
    priority=PluginPriority.CRITICAL,  # 使用最高优先级
    block=False  # 不阻断，确保其他匹配器也能处理
)

@fanqie_global_matcher.handle()
async def handle_fanqie_global(matcher: Matcher, bot: Bot, event: Event):
    """全局处理器，捕获所有可能的番茄小说链接"""
    if not isinstance(event, GroupMessageEvent):
        return
    
    raw_message = str(event.get_message()).strip()
    logger.info(f"全局番茄小说链接检查: {raw_message}")
    
    # 检查是否包含番茄小说相关域名
    domains = ['fanqie', 'changdunovel', 'fanqienovel', 'fqnovel', 'novel.snssdk.com', 'novel.ixigua.com', 'zlink.fqnovel.com']
    contains_domain = any(domain in raw_message for domain in domains)
    
    # 检查是否包含book_id参数
    has_book_id = 'book_id' in raw_message
    
    if contains_domain or has_book_id:
        logger.info(f"检测到疑似番茄小说链接: {raw_message}")
        # 尝试提取book_id
        book_id = extract_book_id(raw_message)
        if book_id:
            logger.info(f"成功提取到番茄小说book_id: {book_id}，将处理链接")
            # 阻断后续处理
            matcher.stop_propagation()
            logger.info("事件传播已阻断，由番茄小说插件处理")
            # 处理链接下载
            await handle_download_novel(bot, event)
            return
        else:
            logger.info("无法提取番茄小说book_id，继续传递事件")

@asynccontextmanager
async def async_tempfile():
    """异步临时文件上下文管理器"""
    temp = tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt')
    try:
        yield temp
    finally:
        temp.close()

@progress_query.handle()
async def handle_progress(matcher: Matcher, bot: Bot, event: Event):
    """处理进度查询请求"""
    if not isinstance(event, GroupMessageEvent):
        return
        
    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)
    
    if "下载进度" in cleaned_message:
        # 匹配到下载进度命令，阻断事件传播
        await conditional_block(matcher, True)
        await handle_progress_query(bot, event)
        await progress_query.finish()
    else:
        # 不匹配则不阻断，允许其他插件处理
        return

@download_novel.handle()
async def handle_download_novel(bot: Bot, event: Event):
    try:
        # 检查是否为群消息
        if not isinstance(event, GroupMessageEvent):
            logger.info("非群聊消息，退出处理")
            return
            
        raw_message = str(event.get_message()).strip()
        logger.info(f"收到番茄小说链接：{raw_message}")
        cleaned_message = html.unescape(raw_message)

        await bot.send(event, "       稍候喵❤\n正在处理您的请求啦")

        try:
            book_id = extract_book_id(cleaned_message)
            if not book_id:
                await bot.send(event, "链接无效，无法提取 book_id，请检查链接格式。")
                return

            book_info = await get_book_info(book_id)
            if not book_info or 'data' not in book_info:
                await bot.send(event, "无法获取小说详情，请检查链接是否正确。")
                return

            book_data = book_info['data']
            original_book_name = book_data['original_book_name']
            
            # 获取目录信息
            catalog_data = await get_catalog(book_id)
            if not catalog_data or 'data' not in catalog_data:
                await bot.send(event, "无法获取小说目录，请检查链接是否正确。")
                return

            chapters = catalog_data['data']['item_data_list']
            if not chapters:
                await bot.send(event, "该小说没有可用章节。")
                return

            # 更新下载状态
            await update_download_status(event.user_id, original_book_name, len(chapters))

            # 使用异步临时文件上下文管理器
            async with async_tempfile() as tmp_file:
                await write_book_content(
                    tmp_file, 
                    book_data, 
                    chapters, 
                    event.user_id, 
                    original_book_name
                )
                tmp_path = tmp_file.name

                try:
                    # 读取文件内容
                    logger.info(f"开始读取临时文件：{tmp_path}")
                    with open(tmp_path, 'rb') as f:
                        file_content = f.read()
                    logger.info(f"文件大小：{len(file_content)} 字节")
                    
                    # 上传到群文件
                    logger.info(f"开始上传群文件，群号：{event.group_id}")

                    # 获取文件夹ID
                    folder_id = await get_folder_id(bot, event.group_id, "小说")
                    
                    # 使用NamedTemporaryFile进行上传
                    safe_name = re.sub(r'[\\/:"*?<>|]+', "_", f"{original_book_name}.txt")
                    
                    # 添加重试机制
                    max_retries = 3
                    upload_success = False
                    temp_path = None
                    
                    try:
                        with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                            temp.write(file_content)
                            temp.flush()
                            os.fsync(temp.fileno())  # 确保写入磁盘
                            temp_path = temp.name
                        
                        # 尝试上传到群文件
                        try:
                            # 使用文件路径方式上传，指定文件夹
                            await bot.call_api(
                                "upload_group_file",
                                group_id=event.group_id,
                                file=temp_path,
                                name=safe_name,
                                folder=folder_id if folder_id else ""
                            )
                            
                            upload_success = True
                            logger.info("群文件上传成功")
                            
                            # 发送成功消息
                            success_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f"\n 《{original_book_name}》\n------------\n  下载完毕了喵~\n已上传至群文件❤\n------------")
                            ])
                            await bot.send(event, success_msg)
                            
                        except Exception as e:
                            logger.error(f"群文件上传失败，错误: {str(e)}")
                            # 直接进入WebDAV上传流程，不进行重试
                    except Exception as e:
                        logger.error(f"创建临时文件失败: {str(e)}")
                    
                    # 如果群文件上传失败，尝试上传到WebDAV
                    if not upload_success:
                        logger.info("尝试将文件上传到WebDAV...")
                        # 确保临时文件存在
                        if not temp_path or not os.path.exists(temp_path):
                            with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                                temp.write(file_content)
                                temp.flush()
                                os.fsync(temp.fileno())
                                temp_path = temp.name
                        
                        # 上传到WebDAV
                        download_url = await upload_to_website(temp_path, safe_name)
                        
                        if download_url:
                            # 发送下载链接
                            web_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f"\n 《{original_book_name}》\n------------\n  下载完毕了喵~\n由于文件内容限制\n无法上传至群文件\n请通过以下链接下载\n------------\n{download_url}")
                            ])
                            await bot.send(event, web_msg)
                        else:
                            # 如果WebDAV上传也失败，发送错误消息
                            error_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f" \n 添材少女 琪露诺有点累！\n WebDAV上传失败，请稍后重试喵～")
                            ])
                            await bot.send(event, error_msg)
                    
                    # 清理临时文件
                    if temp_path and os.path.exists(temp_path):
                        os.remove(temp_path)
                    
                except Exception as outer_e:
                    # 这里添加对外层异常的处理
                    logger.error(f"文件读取或处理过程中发生错误: {str(outer_e)}", exc_info=True)
                    error_msg = Message([
                        MessageSegment.at(event.user_id),
                        MessageSegment.text(f" \n 文件准备过程中出错！\n请稍后重试喵～\n错误: {str(outer_e)}")
                    ])
                    await bot.send(event, error_msg)
            
        except Exception as e:
            logger.error(f"下载小说时发生错误: {str(e)}", exc_info=True)
            # 错误消息也使用 Message 构建
            error_msg = Message([
                MessageSegment.at(event.user_id),
                MessageSegment.text(f" \n 添材少女 琪露诺有点累！\n 请稍等2~3分钟再重试吧喵\n\n: {str(e)}")
            ])
            await bot.send(event, error_msg)
            if 'original_book_name' in locals():
                clear_download_status(event.user_id, original_book_name)

    except Exception as e:
        logger.error(f"下载小说时发生错误: {str(e)}", exc_info=True)
        # 错误消息也使用 Message 构建
        error_msg = Message([
            MessageSegment.at(event.user_id),
            MessageSegment.text(f" 下载过程中发生错误: {str(e)}")
        ])
        await bot.send(event, error_msg)
        if 'original_book_name' in locals():
            clear_download_status(event.user_id, original_book_name)

async def handle_progress_query(bot: Bot, event: Event):
    """处理进度查询请求"""
    user_progress = download_status.get(event.user_id, [])
    progress_text = "咲番ing：\n"
    if not user_progress:
        progress_text += "没有进行中的下载啦\n有想看的书喵？快告诉我"
    else:
        for i, book_progress in enumerate(user_progress, 1):
            progress_text += f"{i}. {book_progress['progress']}\n"
    await bot.send(event, progress_text)

async def update_download_status(user_id: int, book_name: str, total_chapters: int):
    """更新用户的下载状态"""
    if user_id not in download_status:
        download_status[user_id] = []
    
    # 检查是否已存在相同书籍的下载状态
    for progress in download_status[user_id]:
        if progress['book_name'] == book_name:
            progress['total_chapters'] = total_chapters
            progress['current_chapter'] = 0
            progress['progress'] = f"《{book_name}》 (0/{total_chapters})"
            return
    
    # 添加新的下载状态
    download_status[user_id].append({
        'book_name': book_name,
        'total_chapters': total_chapters,
        'current_chapter': 0,
        'progress': f"《{book_name}》 (0/{total_chapters})"
    })

async def update_chapter_progress(user_id: int, book_name: str, current_chapter: int):
    """更新章节下载进度"""
    if user_id in download_status:
        for progress in download_status[user_id]:
            if progress['book_name'] == book_name:
                progress['current_chapter'] = current_chapter
                progress['progress'] = f"《{book_name}》 ({current_chapter}/{progress['total_chapters']})"
                break

def clear_download_status(user_id: int, book_name: str):
    """清理下载状态"""
    if user_id in download_status:
        download_status[user_id] = [p for p in download_status[user_id] if p['book_name'] != book_name]
        if not download_status[user_id]:  # 如果用户没有其他下载任务，删除用户记录
            del download_status[user_id]

def is_fanqie_url(url: str) -> bool:
    """改进的番茄小说链接检测"""
    try:
        # 检查是否包含番茄小说的域名特征
        fanqie_domains = [
            "fanqie", "changdunovel", "fanqienovel", "fqnovel",
            "novel.snssdk.com", "novel.ixigua.com"
        ]
        
        # 检查URL中是否包含这些域名
        for domain in fanqie_domains:
            if domain in url:
                logger.info(f"检测到番茄小说链接，包含域名特征: {domain}")
                return True
                
        # 如果链接中包含多个URL，尝试分割并处理
        possible_urls = url.split('http')
        for possible_url in possible_urls:
            if possible_url:
                # 重新添加http前缀（如果被分割掉了）
                if not possible_url.startswith('http'):
                    possible_url = 'http' + possible_url
                
                parsed_url = urlparse(possible_url)
                query_params = parse_qs(parsed_url.query)
                logger.debug(f"解析后的 URL: {parsed_url}, 查询参数: {query_params}")
                
                # 检查是否包含番茄小说的域名特征
                for domain in fanqie_domains:
                    if domain in parsed_url.netloc:
                        logger.info(f"检测到番茄小说链接，域名: {parsed_url.netloc}")
                        return True
                
                # 检查是否包含book_id参数
                if "book_id" in query_params:
                    logger.info("检测到包含book_id参数的链接")
                    return True
        
        return False
    except Exception as e:
        logger.error(f"URL 解析出错：{e}")
        return False

def is_valid_url(url: str) -> bool:
    """保留原来的URL检测函数，但现在主要依赖is_fanqie_url"""
    return is_fanqie_url(url)

def extract_book_id(link: str) -> str:
    """改进的book_id提取函数，支持多种链接格式"""
    try:
        logger.info(f"尝试从链接提取番茄小说book_id: {link}")
        
        # 如果链接中包含多个URL，尝试分割并处理
        possible_urls = link.split('http')
        for possible_url in possible_urls:
            if possible_url:
                # 重新添加http前缀（如果被分割掉了）
                if not possible_url.startswith('http'):
                    possible_url = 'http' + possible_url
                
                logger.debug(f"分析可能的URL: {possible_url}")
                
                # 解析URL参数
                parsed_url = urlparse(possible_url)
                query_params = parse_qs(parsed_url.query)
                
                # 尝试直接从查询参数中获取book_id
                book_id_list = query_params.get("book_id")
                if book_id_list:
                    book_id = book_id_list[0]
                    logger.info(f"成功提取book_id: {book_id}")
                    return book_id
                
                # 尝试从aid和book_id组合格式中提取
                aid = query_params.get("aid")
                if aid:
                    logger.debug(f"找到aid参数: {aid}")
                    # 重新检查完整URL中的book_id
                    book_id_match = re.search(r'book_id=([0-9]+)', possible_url)
                    if book_id_match:
                        book_id = book_id_match.group(1)
                        logger.info(f"从带aid的URL中提取到book_id: {book_id}")
                        return book_id
                
                # 尝试从URL路径中提取
                path_match = re.search(r'/book/(\d+)', parsed_url.path)
                if path_match:
                    book_id = path_match.group(1)
                    logger.info(f"从URL路径中提取到book_id: {book_id}")
                    return book_id
                
                # 尝试从完整URL中匹配book_id
                full_match = re.search(r'book_id=([0-9]+)', possible_url)
                if full_match:
                    book_id = full_match.group(1)
                    logger.info(f"从完整URL匹配中提取到book_id: {book_id}")
                    return book_id
                
        logger.warning(f"无法从链接提取book_id: {link}")
        return None
    except Exception as e:
        logger.error(f"提取book_id时发生错误：{e}")
        return None

async def get_book_info(book_id: str):
    for i in range(len(API_BASE_URLS)):
        try:
            api_url = get_api_url(i)
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(f"{api_url}/detail?book_id={book_id}")
                response.raise_for_status()
                data = response.json()
                
                # 适配新的API返回格式
                if data and 'data' in data and len(data['data']) > 0:
                    # 构造与旧格式兼容的数据结构
                    compatible_data = {
                        'code': 0,
                        'data': data['data'][0]
                    }
                    logger.debug(f"使用 {api_url} 获取小说信息成功")
                    return compatible_data
                else:
                    logger.warning(f"使用 {api_url} 获取小说信息失败，返回数据格式不正确，尝试下一个接口")
                    continue
        except Exception as e:
            logger.error(f"使用 {api_url} 获取小说详情失败：{e}，尝试下一个接口")
            continue
    
    logger.error("所有API接口都无法获取小说详情")
    return None

async def get_catalog(book_id: str):
    for i in range(len(API_BASE_URLS)):
        try:
            api_url = get_api_url(i)
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(f"{api_url}/catalog?book_id={book_id}")
                response.raise_for_status()
                data = response.json()
                
                # 适配新的API返回格式
                if data and data.get('code') == 0 and 'data' in data and 'item_data_list' in data['data']:
                    logger.debug(f"使用 {api_url} 获取目录成功")
                    return data
                else:
                    logger.warning(f"使用 {api_url} 获取目录失败，返回数据格式不正确，尝试下一个接口")
                    continue
        except Exception as e:
            logger.error(f"使用 {api_url} 获取目录失败：{e}，尝试下一个接口")
            continue
    
    logger.error("所有API接口都无法获取小说目录")
    return None

async def get_chapter_content(item_id: str, client: httpx.AsyncClient, api_url: str):
    """获取章节内容的改进版本"""
    try:
        # 使用本地API获取章节内容
        local_api = "http://************:4386"
        response = await client.get(f"{local_api}/content?item_id={item_id}")
        response.raise_for_status()
        data = response.json()
        
        # 打印返回的数据结构，帮助调试
        logger.debug(f"章节内容API返回数据: {data}")
        
        # 适配本地API返回格式，直接检查data字段中是否包含content
        if data and 'data' in data and 'content' in data['data']:
            logger.debug(f"成功获取章节内容，长度: {len(data['data']['content'])}")
            return data
        else:
            # 如果本地API失败，尝试使用原来的API
            logger.warning(f"本地API返回格式不符合预期，尝试使用原API: {api_url}")
            response = await client.get(f"{api_url}/content?item_id={item_id}")
            response.raise_for_status()
            data = response.json()
            
            if data and 'data' in data and 'content' in data['data']:
                logger.debug(f"成功使用原API获取章节内容，长度: {len(data['data']['content'])}")
                return data
            else:
                logger.warning(f"章节内容返回格式不符合预期: {data}")
                return None
    except Exception as e:
        logger.error(f"获取章节内容失败：{e}")
        try:
            # 如果本地API失败，尝试使用原来的API
            response = await client.get(f"{api_url}/content?item_id={item_id}")
            response.raise_for_status()
            data = response.json()
            
            if data and 'data' in data and 'content' in data['data']:
                logger.debug(f"成功使用原API获取章节内容，长度: {len(data['data']['content'])}")
                return data
            else:
                return None
        except Exception as e2:
            logger.error(f"使用原API获取章节内容也失败：{e2}")
            return None

def process_content(content: str) -> str:
    """改进的内容处理函数"""
    try:
        # 先尝试删除img标签
        # 匹配<img>标签的正则表达式
        img_pattern = re.compile(r'<img[^>]*>.*?</img>|<img[^>]*>')
        content = re.sub(img_pattern, '', content)
        
        # 检查内容是否为HTML格式
        if '<' in content and '>' in content:
            logger.debug("内容疑似HTML格式，使用BeautifulSoup处理")
            # 使用BeautifulSoup处理HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除所有图片div
            for div in soup.find_all('div', {'data-fanqie-type': 'image'}):
                div.decompose()
            
            # 再次移除所有img标签（以防BeautifulSoup未能完全处理）
            for img in soup.find_all('img'):
                img.decompose()
                
            # 获取所有段落
            paragraphs = []
            for p in soup.find_all('p'):
                text = p.get_text().strip()
                if text:
                    paragraphs.append(text)
            
            # 如果没有找到段落，可能是纯文本
            if not paragraphs:
                logger.debug("BeautifulSoup没有找到段落，尝试直接处理文本")
                # 直接按行分割
                paragraphs = [line.strip() for line in content.split('\n') if line.strip()]
                
            # 组合成最终文本
            processed_content = '\n  '.join(paragraphs)
        else:
            # 直接处理纯文本
            logger.debug("内容为纯文本格式，直接处理")
            paragraphs = [line.strip() for line in content.split('\n') if line.strip()]
            processed_content = '\n  '.join(paragraphs)
        
        logger.debug(f"处理后的内容长度: {len(processed_content)}")
        return processed_content
    except Exception as e:
        logger.error(f"处理内容时发生错误: {str(e)}")
        return ""

def convert_word_count(count):
    if int(count) <= 9999:
        return str(count)
    else:
        result = int(count) / 10000
        return "{:.1f}万".format(result)

async def write_book_content(file, book_data, chapters, user_id: int, book_name: str):
    """改进的写入函数，使用本地API批量下载章节内容"""
    # 只写入一次书籍信息
    header = (
        f"书名：{book_data['original_book_name']}\n"
        f"作者：{book_data['author']}\n"
        f"最新章节：{book_data['last_chapter_title']}\n"
        f"简介：{book_data.get('book_abstract_v2') or book_data.get('book_abstract', '')}\n"
        f"总章节数：{len(chapters)}\n"
        f"=" * 50 + "\n\n"
    )
    file.write(header.encode('utf-8'))
    
    # 创建共享的httpx客户端池
    clients = []
    api_usage_count = {}
    
    for i in range(len(API_BASE_URLS)):
        clients.append(httpx.AsyncClient(
            timeout=30,
            limits=httpx.Limits(max_keepalive_connections=100, max_connections=200)
        ))
        api_usage_count[i] = 0
    
    local_client = httpx.AsyncClient(
        timeout=30,
        limits=httpx.Limits(max_keepalive_connections=100, max_connections=200)
    )
    
    local_api = "http://************:9090"
    
    async def download_chapter_batch(chapter_batch):
        """批量下载章节内容"""
        results = {}
        
        # 构建批量请求的item_ids
        item_ids = [chapter['item_id'] for chapter in chapter_batch]
        
        try:
            # 尝试使用本地API批量下载
            response = await local_client.post(f"{local_api}/batch_content", 
                                             json={"item_ids": item_ids})
            response.raise_for_status()
            batch_data = response.json()
            
            if batch_data and 'data' in batch_data:
                for chapter in chapter_batch:
                    item_id = chapter['item_id']
                    if item_id in batch_data['data'] and batch_data['data'][item_id]:
                        content_text = batch_data['data'][item_id].get('content', '')
                        content = process_content(content_text)
                        if content:
                            results[chapter['index']] = {
                                'title': chapter['title'],
                                'content': content
                            }
        except Exception as e:
            logger.error(f"批量下载失败，尝试单个下载: {e}")
            # 如果批量下载失败，回退到单个下载
            results = await download_chapters_individually(chapter_batch)
        
        return results
    
    async def download_chapters_individually(chapter_batch):
        """如果批量下载失败，尝试单个下载"""
        results = {}
        for chapter in chapter_batch:
            try:
                response = await local_client.get(f"{local_api}/content?item_id={chapter['item_id']}")
                response.raise_for_status()
                data = response.json()
                
                if data and 'data' in data and 'content' in data['data']:
                    content_text = data['data']['content']
                    content = process_content(content_text)
                    if content:
                        results[chapter['index']] = {
                            'title': chapter['title'],
                            'content': content
                        }
                        continue
            except Exception:
                pass
            
            # 如果本地API失败，尝试使用备用API
            for i in range(len(API_BASE_URLS)):
                try:
                    api_url = get_api_url(i)
                    client = clients[i]
                    content_data = await get_chapter_content(chapter['item_id'], client, api_url)
                    
                    if content_data and 'data' in content_data and 'content' in content_data['data']:
                        content_text = content_data['data']['content']
                        content = process_content(content_text)
                        if content:
                            api_usage_count[i] += 1
                            results[chapter['index']] = {
                                'title': chapter['title'],
                                'content': content
                            }
                            break
                except Exception as e:
                    logger.error(f"使用API {api_url} 下载章节 {chapter['title']} 失败: {e}")
                    continue
        
        return results

    try:
        batch_size = 50
        total_chapters = len(chapters)
        
        # 为章节添加索引
        for idx, chapter in enumerate(chapters):
            chapter['index'] = idx
        
        for i in range(0, total_chapters, batch_size):
            batch = chapters[i:i + batch_size]
            
            # 批量下载章节
            batch_results = await download_chapter_batch(batch)
            
            # 按顺序写入文件
            for idx in range(i, min(i + batch_size, total_chapters)):
                if idx in batch_results:
                    chapter_data = batch_results[idx]
                    # 写入章节标题和内容，不再重复写入书籍信息
                    chapter_text = f"\n{'='*20} {chapter_data['title']} {'='*20}\n{chapter_data['content']}\n\n"
                    file.write(chapter_text.encode('utf-8'))
                else:
                    chapter_title = chapters[idx]['title']
                    logger.warning(f"章节 {chapter_title} 下载失败")
                    file.write(f"\n{'='*20} {chapter_title} {'='*20}\n[章节内容下载失败]\n\n".encode('utf-8'))
                
                # 更新进度
                await update_chapter_progress(user_id, book_name, idx + 1)
            
            # 添加延迟避免过于频繁的请求
            await asyncio.sleep(0.1)
    
    finally:
        # 关闭所有客户端
        await local_client.aclose()
        for client in clients:
            await client.aclose()
        
        # 输出总体API使用情况
        api_usage_str = ", ".join([f"API{i}: {count}" for i, count in api_usage_count.items() if count > 0])
        logger.info(f"下载完成，备用API使用情况: {api_usage_str}")
        clear_download_status(user_id, book_name)

async def upload_with_retry(bot, group_id, file_path, file_name, original_file_content=None, max_retries=5):
    """带重试机制的文件上传函数，增强版"""
    file_size = os.path.getsize(file_path)
    # 根据文件大小动态调整重试次数，大文件可能需要更多重试
    actual_max_retries = max_retries + min(5, file_size // (1024 * 1024))  # 每MB增加一次重试，最多增加5次
    
    for attempt in range(actual_max_retries):
        try:
            # 在重试前检查文件是否完好
            if attempt > 0 and original_file_content:
                logger.info(f"第{attempt+1}次尝试，重新创建临时文件")
                # 确保文件存在且内容完整
                if os.path.exists(file_path):
                    os.remove(file_path)
                with open(file_path, "wb") as local_file:
                    local_file.write(original_file_content)
            
            # 尝试上传
            logger.info(f"开始第{attempt+1}次上传尝试")
            result = await bot.call_api('upload_group_file', 
                group_id=group_id,
                file=file_path,
                name=file_name
            )
            logger.info(f"群文件上传成功，尝试次数: {attempt+1}，结果: {result}")
            return True
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"上传失败，尝试次数: {attempt+1}/{actual_max_retries}, 错误: {error_msg}")
            
            # 检查特定的错误类型
            if "result" in error_msg and attempt < actual_max_retries - 1:
                logger.info(f"检测到API响应格式错误，等待{2 * (attempt + 1)}秒后重试...")
                await asyncio.sleep(2 * (attempt + 1))  # 指数退避
            elif attempt < actual_max_retries - 1:
                # 其他错误也进行重试
                wait_time = 3 * (attempt + 1)  # 更长的等待时间
                logger.info(f"等待{wait_time}秒后重试...")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"上传失败，已达到最大重试次数 {actual_max_retries}")
                raise Exception(f"文件上传失败，已重试{actual_max_retries}次: {error_msg}")
    
    # 不应该运行到这里，但以防万一
    return False

# 添加获取文件夹ID的函数
async def get_folder_id(bot: Bot, group_id: int, folder_name: str):
    try:
        jsonDate = await bot.call_api("get_group_root_files", group_id=group_id)
        for folder in jsonDate.get("folders", []):
            if folder_name == folder["folder_name"]:
                return folder["folder_id"]
        return await create_folder(bot, group_id, folder_name)
    except Exception as e:
        logger.error(f"获取群文件夹ID失败: {e}")
        return ""

# 添加创建文件夹的函数
async def create_folder(bot: Bot, group_id: int, folder_name: str):
    try:
        jsonDate = await bot.call_api("create_group_file_folder", group_id=group_id, folder_name=folder_name)
        return jsonDate["folder_id"]
    except Exception as e:
        logger.error(f"创建群文件夹失败: {e}")
        return ""

# 上传到WebDAV的函数
async def upload_to_website(file_path, file_name):
    """将文件上传到WebDAV，使用httpx直接实现WebDAV功能，不依赖第三方库"""
    try:
        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # 构建远程文件路径
        remote_path = f"{WEBDAV_FOLDER}/{file_name}"
        webdav_url = f"{WEBDAV_URL}/{remote_path}"
        
        # 构建认证信息
        auth = (WEBDAV_USERNAME, WEBDAV_PASSWORD)
        
        # 首先检查目录是否存在
        try:
            async with httpx.AsyncClient(auth=auth, verify=False) as client:
                # 检查文件夹是否存在
                folder_url = f"{WEBDAV_URL}/{WEBDAV_FOLDER}"
                response = await client.request("PROPFIND", folder_url, headers={"Depth": "0"})
                
                # 如果文件夹不存在，创建它
                if response.status_code == 404:
                    logger.info(f"WebDAV文件夹 {WEBDAV_FOLDER} 不存在，尝试创建")
                    mkdir_response = await client.request("MKCOL", folder_url)
                    if mkdir_response.status_code not in (201, 200, 207):
                        logger.error(f"创建WebDAV文件夹失败: {mkdir_response.status_code}")
                        # 继续尝试上传，可能文件夹已存在
        except Exception as e:
            logger.error(f"检查WebDAV文件夹时发生错误: {str(e)}，尝试直接上传")
        
        # 上传文件
        async with httpx.AsyncClient(auth=auth, verify=False) as client:
            response = await client.put(webdav_url, content=file_content, timeout=60)
            
            if response.status_code in (200, 201, 204):
                # 构建下载链接 (使用alist的Web界面链接)
                base_url = WEBDAV_URL.split('/dav')[0]
                
                # 使用简化路径格式
                # 路径应该是: /印子/琪露诺上传的《小说》/琪露诺上传的《小说》/文件名
                simplified_path = "琪露诺上传的《小说》"
                download_url = f"{base_url}/{simplified_path}/{file_name}"
                
                logger.info(f"文件已成功上传到WebDAV，下载链接: {download_url}")
                return download_url
            else:
                logger.error(f"WebDAV上传失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
    except Exception as e:
        logger.error(f"上传到WebDAV时发生错误: {str(e)}", exc_info=True)
        return None
