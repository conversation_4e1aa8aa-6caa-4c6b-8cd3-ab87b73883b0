import os
import asyncio
import httpx
import re
import random
from urllib.parse import urlparse, parse_qs
import html
from concurrent.futures import ThreadPoolExecutor

# API配置
API_BASE_URLS = [
    "http://************:9090",
    "http://************:9011", 
    "http://************:9022",
    "http://************:4386"  # 本地API
]

# 下载目录
DOWNLOAD_DIR = r"F:\fanfanfanfan"

# 添加并发配置
MAX_CONCURRENT_DOWNLOADS = 30  # 最大并发数
SEMAPHORE = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)

def get_api_url(index: int = 0) -> str:
    return API_BASE_URLS[index % len(API_BASE_URLS)]

def process_content(content_text: str) -> str:
    """处理章节内容"""
    if not content_text:
        return ""
    
    # 解码HTML实体
    content = html.unescape(content_text)
    
    # 移除多余的空白字符
    content = re.sub(r'\s+', ' ', content)
    content = content.replace('\\n', '\n').replace('\\r', '')
    
    # 添加段落分隔
    content = re.sub(r'([。！？])\s*', r'\1\n\n', content)
    
    return content.strip()

async def get_book_info(book_id: str):
    """获取小说信息"""
    for i in range(len(API_BASE_URLS)):
        try:
            api_url = get_api_url(i)
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(f"{api_url}/detail?book_id={book_id}")
                response.raise_for_status()
                data = response.json()
                
                if data and 'data' in data and len(data['data']) > 0:
                    compatible_data = {
                        'code': 0,
                        'data': data['data'][0]
                    }
                    print(f"✓ 使用 {api_url} 获取小说信息成功")
                    return compatible_data
                else:
                    print(f"✗ 使用 {api_url} 获取小说信息失败，尝试下一个接口")
                    continue
        except Exception as e:
            print(f"✗ 使用 {api_url} 获取小说详情失败：{e}")
            continue
    
    print("✗ 所有API接口都无法获取小说详情")
    return None

async def get_catalog(book_id: str):
    """获取小说目录"""
    for i in range(len(API_BASE_URLS)):
        try:
            api_url = get_api_url(i)
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(f"{api_url}/catalog?book_id={book_id}")
                response.raise_for_status()
                data = response.json()
                
                if data and data.get('code') == 0 and 'data' in data and 'item_data_list' in data['data']:
                    print(f"✓ 使用 {api_url} 获取目录成功")
                    return data
                else:
                    print(f"✗ 使用 {api_url} 获取目录失败，尝试下一个接口")
                    continue
        except Exception as e:
            print(f"✗ 使用 {api_url} 获取目录失败：{e}")
            continue
    
    print("✗ 所有API接口都无法获取小说目录")
    return None

async def get_chapter_content_with_retry(item_id: str, title: str):
    """带重试和负载均衡的章节内容获取"""
    # 随机选择API以分散负载
    api_indices = list(range(len(API_BASE_URLS)))
    random.shuffle(api_indices)
    
    async with httpx.AsyncClient(timeout=30) as client:
        for api_index in api_indices:
            try:
                api_url = get_api_url(api_index)
                response = await client.get(f"{api_url}/content?item_id={item_id}")
                response.raise_for_status()
                data = response.json()
                
                if data and 'data' in data and 'content' in data['data']:
                    print(f"✓ 下载成功: {title}")
                    return {
                        'item_id': item_id,
                        'title': title,
                        'content': data['data']['content'],
                        'success': True
                    }
            except Exception as e:
                continue
        
        print(f"✗ 下载失败: {title}")
        return {
            'item_id': item_id,
            'title': title,
            'content': None,
            'success': False
        }

async def download_chapters_batch(chapters, start_idx, batch_size):
    """批量下载章节"""
    batch_chapters = chapters[start_idx:start_idx + batch_size]
    tasks = []
    
    for chapter in batch_chapters:
        task = get_chapter_content_with_retry(chapter['item_id'], chapter['title'])
        tasks.append(task)
    
    return await asyncio.gather(*tasks, return_exceptions=True)

async def download_chapters_batch_optimized(chapters, start_idx, batch_size):
    """优化的批量下载 - 使用批量API"""
    batch_chapters = chapters[start_idx:start_idx + batch_size]
    
    # 尝试批量API
    try:
        item_ids = [chapter['item_id'] for chapter in batch_chapters]
        item_ids_str = ','.join(item_ids)
        
        async with httpx.AsyncClient(timeout=60) as client:
            response = await client.get(f"http://************:4386/content?item_id={item_ids_str}")
            response.raise_for_status()
            data = response.json()
            
            results = []
            if data and 'data' in data:
                batch_content = data['data']
                for chapter in batch_chapters:
                    item_id = chapter['item_id']
                    if item_id in batch_content and 'content' in batch_content[item_id]:
                        content = batch_content[item_id]['content']
                        results.append({
                            'item_id': item_id,
                            'title': chapter['title'],
                            'content': content,
                            'success': True
                        })
                        print(f"✓ 批量下载成功: {chapter['title']}")
                    else:
                        results.append({
                            'item_id': item_id,
                            'title': chapter['title'],
                            'content': None,
                            'success': False
                        })
            return results
    except Exception as e:
        print(f"批量API失败，回退到单个下载: {e}")
        # 回退到原来的方法
        return await download_chapters_batch(chapters, start_idx, batch_size)

async def combine_chapters_to_book(book_dir: str, book_data: dict, chapters: list):
    """将所有章节组合成一个完整的小说文件 - 按目录顺序"""
    print("开始组合章节文件...")
    
    # 创建完整小说文件
    book_name = book_data['original_book_name']
    safe_name = re.sub(r'[\\/:"*?<>|]+', "_", book_name)
    book_file_path = os.path.join(book_dir, f"{safe_name}_complete.txt")
    
    try:
        with open(book_file_path, 'w', encoding='utf-8') as book_file:
            # 只写入一次书籍信息头部
            header = (
                f"书名：{book_data['original_book_name']}\n"
                f"作者：{book_data['author']}\n"
                f"最新章节：{book_data.get('last_chapter_title', '未知')}\n"
                f"简介：{book_data.get('book_abstract_v2') or book_data.get('book_abstract', '暂无简介')}\n"
                f"总章节数：{len(chapters)}\n"
                f"=" * 50 + "\n\n"
            )
            book_file.write(header)
            
            # 按目录顺序读取章节文件
            found_chapters = 0
            for i, chapter in enumerate(chapters, 1):
                item_id = chapter['item_id']
                chapter_title = chapter['title']
                filename = f"{item_id}.txt"
                chapter_path = os.path.join(book_dir, filename)
                
                if os.path.exists(chapter_path):
                    try:
                        with open(chapter_path, 'r', encoding='utf-8') as chapter_file:
                            content = chapter_file.read().strip()
                            if content:
                                # 彻底清理章节文件中的所有书籍信息
                                lines = content.split('\n')
                                clean_content = []
                                skip_mode = False
                                
                                for line in lines:
                                    line = line.strip()
                                    # 跳过所有可能的书籍信息行
                                    if (line.startswith('书名：') or 
                                        line.startswith('作者：') or 
                                        line.startswith('最新章节：') or 
                                        line.startswith('简介：') or 
                                        line.startswith('总章节数：') or 
                                        line.startswith('章节标题:') or
                                        line == '=' * 50):
                                        skip_mode = True
                                        continue
                                    
                                    # 如果遇到空行且在跳过模式，继续跳过
                                    if skip_mode and line == '':
                                        continue
                                    
                                    # 退出跳过模式，开始收集内容
                                    skip_mode = False
                                    clean_content.append(line)
                                
                                # 重新组合内容
                                content = '\n'.join(clean_content).strip()
                                
                                # 写入章节
                                if content:  # 确保有内容才写入
                                    book_file.write(f"\n{'='*20} {chapter_title} {'='*20}\n")
                                    book_file.write(f"{content}\n\n")
                                    found_chapters += 1
                        
                        if found_chapters % 50 == 0:
                            print(f"已组合 {found_chapters} 章节")
                            
                    except Exception as e:
                        print(f"✗ 读取章节文件 {filename} 失败: {e}")
                        continue
                else:
                    print(f"✗ 章节文件不存在: {filename}")
        
        print(f"✓ 完整小说文件已生成: {book_file_path}")
        print(f"✓ 成功组合 {found_chapters}/{len(chapters)} 章节")
        return book_file_path
        
    except Exception as e:
        print(f"✗ 组合章节文件失败: {e}")
        return None

async def download_novel(book_id: str):
    """分批并发下载小说 - 支持增量更新"""
    print(f"开始下载小说，Book ID: {book_id}")
    
    # 获取小说信息和目录
    book_info = await get_book_info(book_id)
    if not book_info or 'data' not in book_info:
        print("✗ 无法获取小说详情")
        return
    
    book_data = book_info['data']
    book_name = book_data['original_book_name']
    author = book_data['author']
    
    print(f"小说名称: {book_name}")
    print(f"作者: {author}")
    
    catalog_data = await get_catalog(book_id)
    if not catalog_data or 'data' not in catalog_data:
        print("✗ 无法获取小说目录")
        return
    
    chapters = catalog_data['data']['item_data_list']
    if not chapters:
        print("✗ 该小说没有可用章节")
        return
    
    print(f"总章节数: {len(chapters)}")
    
    # 创建下载目录
    book_dir = os.path.join(DOWNLOAD_DIR, book_id)
    os.makedirs(book_dir, exist_ok=True)
    print(f"下载目录: {book_dir}")
    
    # 检查已存在的章节
    existing_files = set()
    if os.path.exists(book_dir):
        existing_files = {f for f in os.listdir(book_dir) if f.endswith('.txt') and not f.endswith('_complete.txt')}
    
    total_existing = len(existing_files)
    print(f"已下载章节: {total_existing}个")
    print(f"需要下载: {len(chapters) - total_existing}个")
    
    batch_size = 100
    success_count = 0
    failed_count = 0
    skipped_count = 0
    
    # 创建专用客户端
    local_client = httpx.AsyncClient(
        timeout=60,
        limits=httpx.Limits(max_keepalive_connections=200, max_connections=400)
    )
    
    try:
        for i in range(0, len(chapters), batch_size):
            print(f"处理批次 {i//batch_size + 1}/{(len(chapters) + batch_size - 1)//batch_size}")
            
            results = await download_super_batch(chapters, i, batch_size, local_client, book_dir)
            
            # 处理结果并保存文件
            for result in results:
                if isinstance(result, Exception):
                    print(f"章节下载异常: {result}")
                    failed_count += 1
                    continue
                
                if result.get('skipped', False):
                    skipped_count += 1
                    continue
                    
                if result['success']:
                    # 处理内容
                    content = process_content(result['content'])
                    
                    # 保存章节文件
                    filename = f"{result['item_id']}.txt"
                    filepath = os.path.join(book_dir, filename)
                    
                    try:
                        with open(filepath, 'w', encoding='utf-8') as f:
                            # 只写入章节标题和内容，不写入书籍信息
                            f.write(f"章节标题: {chapters[success_count]['title']}\n\n{content}")
                        success_count += 1
                    except Exception as e:
                        print(f"✗ 保存失败: {e}")
                        failed_count += 1
                else:
                    failed_count += 1
            
            current_progress = min(i + batch_size, len(chapters))
            print(f"批次完成，进度: {current_progress}/{len(chapters)}")
            
    finally:
        await local_client.aclose()
    
    print(f"\n下载完成！")
    print(f"新下载: {success_count}, 跳过: {skipped_count}, 失败: {failed_count}")
    
    # 组合成完整小说文件 - 传入章节列表保持顺序
    book_file_path = await combine_chapters_to_book(book_dir, book_data, chapters)
    
    if book_file_path:
        print(f"✓ 完整小说《{book_name}》已保存: {book_file_path}")
    else:
        print(f"✗ 组合完整小说文件失败")
    
    print(f"章节文件目录: {book_dir}")

async def download_super_batch(chapters, start_idx, batch_size, client, book_dir):
    """超级批量下载 - 支持增量更新"""
    batch_chapters = chapters[start_idx:start_idx + batch_size]
    
    # 过滤出未下载的章节
    chapters_to_download = []
    skipped_count = 0
    
    for chapter in batch_chapters:
        filename = f"{chapter['item_id']}.txt"
        filepath = os.path.join(book_dir, filename)
        
        if os.path.exists(filepath):
            skipped_count += 1
            # 创建已存在的结果
            results_existing = {
                'item_id': chapter['item_id'],
                'title': chapter['title'],
                'content': None,
                'success': True,
                'skipped': True
            }
        else:
            chapters_to_download.append(chapter)
    
    if skipped_count > 0:
        print(f"跳过已下载章节: {skipped_count}个")
    
    # 如果没有需要下载的章节，返回空结果
    if not chapters_to_download:
        return []
    
    try:
        item_ids = [chapter['item_id'] for chapter in chapters_to_download]
        item_ids_str = ','.join(item_ids)
        
        response = await client.get(f"http://************:4386/content?item_id={item_ids_str}")
        response.raise_for_status()
        data = response.json()
        
        if 'data' in data and data['data']:
            results = []
            for chapter in chapters_to_download:
                item_id = chapter['item_id']
                if item_id in data['data'] and 'content' in data['data'][item_id]:
                    content = data['data'][item_id]['content']
                    processed_content = process_content(content)
                    results.append({
                        'item_id': item_id,
                        'title': chapter['title'],
                        'content': processed_content,  # 只返回处理后的内容
                        'success': True,
                        'skipped': False
                    })
                    print(f"✓ 批量下载成功: {chapter['title']}")
                else:
                    results.append({
                        'item_id': item_id,
                        'title': chapter['title'],
                        'content': None,
                        'success': False,
                        'skipped': False
                    })
            return results
    except Exception as e:
        print(f"批量下载失败: {e}")
        return []



def main():
    """主函数"""
    print("=" * 50)
    print("番茄小说下载器")
    print("=" * 50)
    
    while True:
        try:
            book_id = input("\n请输入小说的 Book ID (输入 'quit' 退出): ").strip()
            
            if book_id.lower() == 'quit':
                print("再见！")
                break
            
            if not book_id:
                print("Book ID 不能为空！")
                continue
            
            if not book_id.isdigit():
                print("Book ID 应该是数字！")
                continue
            
            # 确保下载目录存在
            os.makedirs(DOWNLOAD_DIR, exist_ok=True)
            
            # 开始下载
            asyncio.run(download_novel(book_id))
            
        except KeyboardInterrupt:
            print("\n\n下载被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()


def clean_existing_book_file(file_path: str):
    """清理已存在的错误格式文件 - 移除重复的书籍头部信息"""
    if not os.path.exists(file_path):
        return

    print(f"正在清理文件: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式找到所有书籍头部信息块
        import re

        # 匹配完整的书籍信息头部块（从"书名："开始到"="分隔线结束）
        header_pattern = r'书名：[^\n]*\n作者：[^\n]*\n最新章节：[^\n]*\n简介：[^\n]*\n总章节数：[^\n]*\n=+\n*'

        # 找到所有匹配的头部
        headers = list(re.finditer(header_pattern, content))

        if len(headers) <= 1:
            print("✓ 文件无需清理（没有重复头部）")
            return

        print(f"发现 {len(headers)} 个重复的书籍头部信息")

        # 保留第一个头部，移除其他的
        cleaned_content = content

        # 从后往前删除，避免位置偏移问题
        for header_match in reversed(headers[1:]):
            start, end = header_match.span()
            cleaned_content = cleaned_content[:start] + cleaned_content[end:]

        # 重新写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        print(f"✓ 文件清理完成，移除了 {len(headers) - 1} 个重复头部")

    except Exception as e:
        print(f"✗ 清理文件失败: {e}")

if __name__ == "__main__":
    main()
    # 清理已存在的错误格式文件
    for root, _, files in os.walk(DOWNLOAD_DIR):
        for file in files:
            if file.endswith('_complete.txt'):
                file_path = os.path.join(root, file)
                clean_existing_book_file(file_path)








